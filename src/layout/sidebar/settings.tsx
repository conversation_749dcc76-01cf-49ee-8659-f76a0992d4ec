import React, { CSSProperties, useCallback } from "react";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router";

import type { SelectEventHandler } from "rc-menu/lib/interface";
import {
  Menu,
  ConfigProvider,
  theme,
  type ThemeConfig,
  type MenuProps
} from "antd";

import { Button, Typography } from "@/atoms";
import { StorageService } from "@/services";

const storageService = new StorageService();

const customTheme = {
  components: {
    Menu: {
      groupTitleFontSize: 13,
      iconMarginInlineEnd: 6,
      itemHeight: 28,
      margin: 8,
      itemMarginInline: 0,
      itemPaddingInline: 12
    }
  }
} satisfies ThemeConfig;

const Sidebar = React.forwardRef<
  App.Layout.SidebarRef,
  App.Layout.SidebarProps
>((_props, ref) => {
  const navigate = useNavigate();
  const { token } = theme.useToken();
  const items: MenuProps["items"] = [
    {
      key: "account-setting",
      type: "group",
      label: "<PERSON><PERSON><PERSON> khoản",
      children: [
        {
          key: "account-setting:account-info",
          icon: <Icon icon="icon-park-outline:water-level" />,
          label: "Thông tin tài khoản"
        },
        {
          key: "account-setting:personal-info",
          icon: <Icon icon="icon-park-outline:pie-one" />,
          label: "Thông tin cá nhân"
        },
        {
          key: "account-setting:personal-profile",
          icon: <Icon icon="icon-park-outline:round-mask" />,
          label: "Hồ sơ cá nhân"
        }
      ]
    },
    {
      key: "notification",
      icon: <Icon icon="icon-park-outline:tea-drink" />,
      label: "Thông báo",
    },
    {
      key: "about",
      icon: <Icon icon="icon-park-outline:triangle" />,
      label: "About",
    }
  ];

  const titleColor = React.useMemo<CSSProperties>(
    () => ({
      color: token.colorTextSecondary
    }),
    [token]
  );

  const onSelect = React.useCallback<SelectEventHandler>(
    ({ key }) => {
      navigate(`settings/${key.split(":").pop()}` as string);
    },
    [navigate]
  );

  React.useImperativeHandle(ref, () => ({}), []);

  const handleClickBack = useCallback(() => {
    const backPath = storageService.local.load("prevPath") || "/";
    navigate(backPath);
    storageService.local.delete("prevPath");
  }, [])

  return (
    <div className="h-full flex flex-col border-r border-secondary">
      <div className="flex items-center border-b h-[40px]">
        <Button
          type="link"
          onClick={handleClickBack}
          icon={
            <Icon
              icon="icon-park-outline:left"
              className="text-gray-600"
              width={20}
            />
          }
        />
        <Typography.Title
          level={2}
          type="secondary"
          style={titleColor}
          className="text-center text-lg mb-1 cursor-pointer"
        >
          Settings
        </Typography.Title>
      </div>
      <ConfigProvider theme={customTheme}>
        <Menu
          items={items}
          onSelect={onSelect}
          className="bg-light-primary overflow-y-auto flex-auto"
        />
      </ConfigProvider>
      <div className="flex flex-col border-t border-secondary pt-2">
        <Typography.Text type="secondary" className="text-center">
          © 2025 Mansa Việt Nam
        </Typography.Text>
        <Typography.Text type="secondary" className="text-center">
          Powered by AIT Software
        </Typography.Text>
      </div>
    </div>
  );
});

Sidebar.displayName = "Sidebar";

export default React.memo(Sidebar);