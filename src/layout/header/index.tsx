import React, { useCallback } from 'react'
import { Icon } from '@iconify/react'
import { useNavigate } from 'react-router';

import { Button, Avatar } from '@/atoms'
// import events, { BreadcrumbChanged } from '@/events'
import { AppBreadcrumb } from '@/components';
import { StorageService } from "@/services";

const storageService = new StorageService();

const AppHeaderView = React.forwardRef<
  App.Layout.AppHeaderRef,
  App.Layout.AppHeaderProps
>((_props, ref) => {
  const navigate = useNavigate();
  React.useImperativeHandle(ref, () => ({}), [])

  // React.useEffect(() => {
  //   const onBreadcrumbChanged: EventListener = (e) => {
  //     setBreadcumb((e as unknown as BreadcrumbChanged).detail.breadcrumb)
  //   }
  //   events.addEventListener(BreadcrumbChanged.Name, onBreadcrumbChanged)
  //   return () => events.removeEventListener(BreadcrumbChanged.Name, onBreadcrumbChanged)
  // }, [])

  const handleClickSetting = useCallback(() => {
    storageService.local.save("prevPath", window.location.pathname);
    navigate('/settings');
  }, [])

  return (
    <div className='h-full border-b border-secondary flex items-center justify-between pr-2 pl-4'>
      {/* {breadcrumb || <div>App Header</div>} */}
      <AppBreadcrumb />

      <div className='flex gap-2 items-center'>
        <div className='flex items-center gap-1'>
          <Button shape='circle' size='small' type={'text'}>
            <Avatar size={'small'}>M</Avatar>
          </Button>
          <Button shape='circle' size='small' type={'text'}>
            <Icon icon='icon-park-outline:remind' />
          </Button>
          <Button shape='circle' size='small' type={'text'}>
            <Icon icon='icon-park-outline:help' />
          </Button>
        </div>
        <div className='w-[1px] h-[16px] border-l border-secondary'></div>
        <div className='flex items-center gap-1'>
          <Button shape='circle' size='small' type={'text'}>
            <Icon icon='icon-park-outline:translate' />
          </Button>
          <Button shape='circle' size='small' type={'text'}>
            <Icon icon='icon-park-outline:brightness' />
          </Button>
          <Button shape='circle' size='small' type={'text'} onClick={handleClickSetting}>
            <Icon icon='icon-park-outline:setting-two' />
          </Button>
          <Button shape='circle' size='small' type={'text'}>
            <Icon icon='icon-park-outline:more-one' />
          </Button>
        </div>
      </div>
    </div>
  )
})

AppHeaderView.displayName = 'AppHeader'

export default AppHeaderView
