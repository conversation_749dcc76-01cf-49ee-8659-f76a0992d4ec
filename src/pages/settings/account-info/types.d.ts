declare namespace App.Pages.Setting {
  export type AccountInfoProps = {}
  export type AccountInfoRef = {}

  // Form data types
  export interface ChangePasswordForm {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }

  export interface AccountInfoData {
    id: string;
    username: string;
    email: string;
    phone?: string;
    isEmailVerified: boolean;
    isPhoneVerified: boolean;
    createdAt: string;
    lastLoginAt?: string;
  }

  // API Response types
  export interface AccountInfoResponse {
    success: boolean;
    data?: AccountInfoData;
    message?: string;
    error?: any;
  }

  export interface ChangePasswordResponse {
    success: boolean;
    message?: string;
    error?: any;
  }
}

