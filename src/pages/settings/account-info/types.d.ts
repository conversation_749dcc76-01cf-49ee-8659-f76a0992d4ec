declare namespace App.Pages.Setting {
  export type AccountInfoProps = {}
  export type AccountInfoRef = {}

  // Form đổi mật khẩu
  export type ChangePasswordForm = {
    current: string
    new: string
    confirm: string
  }

  // Form sửa thông tin cá nhân
  export type PersonalInfoForm = {
    fullName: string
    dateOfBirth?: string
    gender?: 'male' | 'female' | 'other'
    phone?: string
    address?: string
    avatar?: string
  }

  // Dữ liệu user từ backend
  export type UserProfileData = {
    username: string
    email: string
    phone: string
    verificationStatus: boolean
    fullName?: string
    dateOfBirth?: string
    gender?: 'male' | 'female' | 'other'
    address?: string
    avatar?: string
    createdAt?: string
    lastLoginAt?: string
  }

  // Response khi lấy thông tin user
  export type UserProfileResponse = {
    success: boolean
    data: UserProfileData
    message?: string
  }

  // Response khi cập nhật thông tin
  export type UpdateProfileResponse = {
    success: boolean
    data: UserProfileData
    message?: string
  }

  // Response khi đổi mật khẩu
  export type ChangePasswordResponse = {
    success: boolean
    message?: string
  }
} 