declare namespace App.Pages.Setting {
  export type AccountInfoProps = {}
  export type AccountInfoRef = {}

  // Form đổi mật khẩu
  export type ChangePasswordForm = {
    current: string
    new: string
    confirm: string
  }

  // Form sửa thông tin cá nhân
  export type PersonalInfoForm = {
    fullName: string
    dateOfBirth?: string
    gender?: 'male' | 'female' | 'other'
    phone?: string
    address?: string
    avatar?: string
  }

  // Dữ liệu user từ backend
  export type UserProfileData = {
    username: string
    email: string
    phone: string
    verificationStatus: boolean
    fullName?: string
    dateOfBirth?: string
    gender?: 'male' | 'female' | 'other'
    address?: string
    avatar?: string
    createdAt?: string
    lastLoginAt?: string
  }

  // Response khi lấy thông tin user
  export type UserProfileResponse = {
    success: boolean
    data: UserProfileData
    message?: string
  }

  // Response khi cập nhật thông tin
  export type UpdateProfileResponse = {
    success: boolean
    data: UserProfileData
    message?: string
  }

  // Response khi đổi mật khẩu
  export type ChangePasswordResponse = {
    success: boolean
    message?: string
  }
} import BaseService from '../base'

export default class SettingsService extends BaseService {
  static SETTINGS_ENDPOINT = '/settings'

  getSettingsEndpointFor = (...paths: string[]) => {
    return [SettingsService.SETTINGS_ENDPOINT, ...paths].filter(Boolean).join('/')
  }

  // Kỹ năng 1: Lấy thông tin user
  getUserProfile = () => {
    return new Promise<App.Pages.Setting.UserProfileResponse>((resolve, reject) => {
      try {
        return this.privateSender.get(this.getSettingsEndpointFor('profile')).then(res => {
          resolve({
            success: true,
            data: res.data,
            message: res.data?.message
          })
        }).catch(error => {
          resolve({
            success: false,
            error: error.response?.data || error.message,
            message: error.response?.data?.message || 'Có lỗi xảy ra khi lấy thông tin'
          })
        })
      } catch (e) {
        console.error('getUserProfile error:', e)
        reject({ success: false, error: e })
      }
    })
  }

  // Kỹ năng 2: Cập nhật thông tin user
  updateProfile = (payload: App.Pages.Setting.PersonalInfoForm) => {
    return new Promise<App.Pages.Setting.UpdateProfileResponse>((resolve, reject) => {
      try {
        return this.privateSender.put(this.getSettingsEndpointFor('profile'), payload).then(res => {
          resolve({
            success: true,
            data: res.data,
            message: res.data?.message || 'Cập nhật thông tin thành công'
          })
        }).catch(error => {
          resolve({
            success: false,
            error: error.response?.data || error.message,
            message: error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật thông tin'
          })
        })
      } catch (e) {
        console.error('updateProfile error:', e)
        reject({ success: false, error: e })
      }
    })
  }

  // Kỹ năng 3: Đổi mật khẩu
  changePassword = (payload: App.Pages.Setting.ChangePasswordForm) => {
    return new Promise<App.Pages.Setting.ChangePasswordResponse>((resolve, reject) => {
      try {
        return this.privateSender.post(this.getSettingsEndpointFor('change-password'), {
          currentPassword: payload.current,
          newPassword: payload.new
        }).then(res => {
          resolve({
            success: true,
            message: res.data?.message || 'Đổi mật khẩu thành công'
          })
        }).catch(error => {
          resolve({
            success: false,
            error: error.response?.data || error.message,
            message: error.response?.data?.message || 'Có lỗi xảy ra khi đổi mật khẩu'
          })
        })
      } catch (e) {
        console.error('changePassword error:', e)
        reject({ success: false, error: e })
      }
    })
  }
}