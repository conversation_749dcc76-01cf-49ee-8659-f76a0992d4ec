import React, { useState, useEffect } from 'react';
import { Form, Button, message, Row, Col } from 'antd';
import { Input, Select, DatePicker } from '@/atoms';
import SettingsService from '@/services/settings';
import dayjs from 'dayjs';

interface PersonalInfoFormProps {
  initialData?: App.Pages.Setting.PersonalInfoData;
  onSuccess?: (data: App.Pages.Setting.PersonalInfoData) => void;
  onCancel?: () => void;
}

const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({ 
  initialData, 
  onSuccess, 
  onCancel 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const settingsService = new SettingsService();

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        ...initialData,
        dateOfBirth: initialData.dateOfBirth ? dayjs(initialData.dateOfBirth) : null
      });
    }
  }, [initialData, form]);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const payload: App.Pages.Setting.PersonalInfoForm = {
        fullName: values.fullName,
        dateOfBirth: values.dateOfBirth ? values.dateOfBirth.format('YYYY-MM-DD') : undefined,
        gender: values.gender,
        phone: values.phone,
        address: values.address
      };

      const response = await settingsService.updatePersonalInfo(payload);
      
      if (response.success && response.data) {
        message.success(response.message || 'Cập nhật thông tin thành công');
        onSuccess?.(response.data);
      } else {
        message.error(response.message || 'Có lỗi xảy ra khi cập nhật thông tin');
      }
    } catch (error) {
      console.error('Update personal info error:', error);
      message.error('Có lỗi xảy ra khi cập nhật thông tin');
    } finally {
      setLoading(false);
    }
  };

  const validatePhone = (_: any, value: string) => {
    if (!value) return Promise.resolve();
    
    const phoneRegex = /^[0-9]{10,11}$/;
    if (!phoneRegex.test(value)) {
      return Promise.reject(new Error('Số điện thoại không hợp lệ'));
    }
    return Promise.resolve();
  };

  const genderOptions = [
    { label: 'Nam', value: 'male' },
    { label: 'Nữ', value: 'female' },
    { label: 'Khác', value: 'other' }
  ];

  return (
    <div className="max-w-2xl">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Họ và tên"
              name="fullName"
              rules={[
                { required: true, message: 'Vui lòng nhập họ và tên' },
                { min: 2, message: 'Họ tên phải có ít nhất 2 ký tự' }
              ]}
            >
              <Input 
                placeholder="Nhập họ và tên"
                size="large"
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label="Ngày sinh"
              name="dateOfBirth"
            >
              <DatePicker 
                placeholder="Chọn ngày sinh"
                size="large"
                style={{ width: '100%' }}
                format="DD/MM/YYYY"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Giới tính"
              name="gender"
            >
              <Select
                placeholder="Chọn giới tính"
                size="large"
                options={genderOptions}
              />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              label="Số điện thoại"
              name="phone"
              rules={[
                { validator: validatePhone }
              ]}
            >
              <Input 
                placeholder="Nhập số điện thoại"
                size="large"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="Địa chỉ"
          name="address"
        >
          <Input.TextArea 
            placeholder="Nhập địa chỉ"
            rows={3}
            size="large"
          />
        </Form.Item>

        <Form.Item className="mb-0">
          <div className="flex gap-3">
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              size="large"
            >
              Cập nhật thông tin
            </Button>
            {onCancel && (
              <Button 
                onClick={onCancel}
                size="large"
              >
                Hủy
              </Button>
            )}
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default PersonalInfoForm;
